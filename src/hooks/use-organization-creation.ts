'use client'

import { useState, useEffect } from 'react'
import { authClient } from '@/lib/auth-client'
import { toast } from 'sonner'
import {
  getDisplayName,
  getOrganizationLogo,
  generateOrganizationSlug,
} from '@/lib/email-utils'

interface OrganizationCreationState {
  isCreatingOrganization: boolean
  needsOrganization: boolean
  organizationCreated: boolean
  error: string | null
}

/**
 * Hook to track and handle organization creation for first-time users.
 * This ensures the startup loader stays visible during the entire organization setup process.
 */
export function useOrganizationCreation() {
  const [state, setState] = useState<OrganizationCreationState>({
    isCreatingOrganization: false,
    needsOrganization: false,
    organizationCreated: false,
    error: null,
  })

  // Get session and organization data
  const { data: session, isPending: sessionLoading } = authClient.useSession()
  const { data: activeOrganization, isPending: orgLoading } =
    authClient.useActiveOrganization()
  const { data: organizations, isPending: organizationsLoading } =
    authClient.useListOrganizations()

  // Determine if organization creation is needed
  useEffect(() => {
    if (sessionLoading || orgLoading || organizationsLoading) {
      return // Still loading, wait
    }

    if (!session?.user?.emailVerified) {
      return // User not verified, no action needed
    }

    // Check if user needs an organization
    const hasNoOrganizations = !organizations || organizations.length === 0
    const hasNoActiveOrganization = !activeOrganization

    if (
      hasNoOrganizations &&
      hasNoActiveOrganization &&
      !state.organizationCreated
    ) {
      setState(prev => ({ ...prev, needsOrganization: true }))
    } else if (activeOrganization && state.needsOrganization) {
      // Organization exists now, reset the need flag
      setState(prev => ({
        ...prev,
        needsOrganization: false,
        organizationCreated: true,
      }))
    }
  }, [
    session,
    activeOrganization,
    organizations,
    sessionLoading,
    orgLoading,
    organizationsLoading,
    state.organizationCreated,
    state.needsOrganization,
  ])

  // Auto-create organization when needed
  useEffect(() => {
    if (
      state.needsOrganization &&
      !state.isCreatingOrganization &&
      !state.organizationCreated
    ) {
      createOrganization()
    }
  }, [
    state.needsOrganization,
    state.isCreatingOrganization,
    state.organizationCreated,
  ])

  const createOrganization = async () => {
    if (!session?.user) {
      setState(prev => ({ ...prev, error: 'No user session found' }))
      return
    }

    try {
      setState(prev => ({ ...prev, isCreatingOrganization: true, error: null }))

      console.log('Creating workspace for user:', session.user.email)

      // Generate organization details
      const orgName = getDisplayName(
        session.user.name || '',
        session.user.email || ''
      )
      const orgSlug = generateOrganizationSlug(
        session.user.name || '',
        session.user.email || ''
      )
      const orgLogo =
        session.user.image ||
        getOrganizationLogo(session.user.name || '', session.user.email || '')

      // Create organization using Better Auth
      const organization = await authClient.organization.create({
        name: orgName,
        slug: orgSlug,
        logo: orgLogo,
        userId: session.user.id,
      })

      if (organization?.data?.id) {
        // Set the newly created organization as active
        await authClient.organization.setActive({
          organizationId: organization.data.id,
        })

        console.log(
          `Workspace "${orgName}" created and set as active for ${session.user.email}`
        )

        // Call the setActiveOrganization API to ensure server-side state is updated
        try {
          await fetch('/api/set-active-organization', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
          })
          window.location.reload()
        } catch (error) {
          console.warn('Error calling setActiveOrganization API:', error)
          console.error('Failed to set organization:', error)
          setState(prev => ({
            ...prev,
            isCreatingOrganization: false,
            error:
              error instanceof Error
                ? error.message
                : 'Failed to create workspace',
          }))
        }
      } else {
        throw new Error('Organization created but ID not returned')
      }
    } catch (error) {
      console.error('Failed to create organization:', error)
      setState(prev => ({
        ...prev,
        isCreatingOrganization: false,
        error:
          error instanceof Error ? error.message : 'Failed to create workspace',
      }))

      // Don't show error toast to avoid overwhelming user - they can still use the app
      console.warn(
        'User can still use the app, workspace will be created on next action'
      )
    }
  }

  return {
    isCreatingOrganization: state.isCreatingOrganization,
    needsOrganization: state.needsOrganization,
    organizationCreated: state.organizationCreated,
    error: state.error,
    // Expose the creation function for manual triggering if needed
    createOrganization,
  }
}
